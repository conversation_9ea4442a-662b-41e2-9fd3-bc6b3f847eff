import os
import sys

sys.path.append(os.getcwd())

from src.vault_data import VaultData
from src.market_data import MarketData
from config import config
from utils.web3_connector import Web3Connector
from utils.web3_contract import Web3Contract
from strategies.equalize_util.equalize_util import EqualizeUtil
from strategies.priority_util.priority_util import PrioritizedUtilizationStrategy
from utils.fordefi_transaction_helpers import build_and_send_transaction

from log import get_logger

logger = get_logger(__name__)

STRATEGY = PrioritizedUtilizationStrategy


class ReallocationBot:
    def __init__(self):
        self.vault_data = VaultData(
            vault_address=config.VAULT_ADDRESS, chain_id=config.PRIMARY_CHAIN_ID
        ).fetch_vault_data()
        self.strategy = STRATEGY()

    def execute_reallocation(self):
        client = Web3Connector(
            provider_url=os.getenv(f"RPC_URL_{config.PRIMARY_CHAIN_ID}", ""),
        ).get_client()

        new_allocations = self.strategy.find_reallocation(self.vault_data)

        # Format parameters to fit the contract function invocation
        params = []
        for new_allocation in new_allocations:
            params.append(
                tuple(
                    [
                        tuple(list(new_allocation[0].values())),
                        int(new_allocation[1]),
                    ]
                )
            )

        contract = Web3Contract(client, config.VAULT_ADDRESS, config.VAULT_ABI)
        wallet_address = config.WALLET_ADDRESS

        function_name = "reallocate"
        params = [params]
        contract_function = getattr(contract.contract.functions, function_name)

        try:
            tx_hash = build_and_send_transaction(
                config.PRIMARY_CHAIN_ID,
                config.FORDEFI_VAULT_ID,
                client,
                contract_function,
                params,
                wallet_address,
            )

            logger.info(f"Transaction hash: {tx_hash}")
        except Exception as e:
            logger.error(f"Couldn't revoke the cap: {e}")
            return


if __name__ == "__main__":
    bot = ReallocationBot()
    bot.execute_reallocation()
