# Use the official Python image as the base image
FROM python:3.9-slim

# Set the working directory inside the container
WORKDIR /app

# Copy the requirements file into the container
COPY requirements.txt ./

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the entire project into the container
COPY . .

# Set the entry point for the Lambda function
CMD ["python", "-c", "from src.bot import ReallocationBot; bot = ReallocationBot(); bot.execute_reallocation()"]
